{"name": "migranium-landing-page", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --port 8050", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "npx husky install"}, "dependencies": {"@azure/msal-browser": "^3.27.0", "@hookform/resolvers": "^3.9.0", "@intercom/messenger-js-sdk": "^0.0.14", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@react-oauth/google": "^0.12.1", "@tanstack/react-query": "^5.51.23", "aos": "^2.3.4", "autoprefixer": "^10.4.20", "axios": "^1.7.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cookie": "0.7.0", "cookies-next": "^4.2.1", "countries-and-timezones": "^3.6.0", "country-state-city": "^3.2.1", "cross-spawn": "7.0.5", "dayjs": "^1.11.12", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.15.0", "gsap": "^3.13.0", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.427.0", "mingcute_icon": "^2.9.5", "moment-timezone": "^0.5.45", "nanoid": "3.3.8", "next": "14.2.21", "react": "^18", "react-calendly": "^4.3.1", "react-day-picker": "^9.0.8", "react-dom": "^18", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.52.2", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "react-icons-sax": "^0.0.5", "react-intersection-observer": "^9.13.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "use-places-autocomplete": "^4.0.1", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@types/aos": "^3.0.7", "@types/eslint": "^9.6.0", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-config-standard-with-typescript": "^43.0.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^50.2.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "husky": "^8.0.3", "lint-staged": "^15.2.9", "postcss": "^8.4.41", "postcss-import": "^16.1.0", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "tailwindcss": "^3.4.1", "typescript": "^5"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint .", "prettier . --write"]}}